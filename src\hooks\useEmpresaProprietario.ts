'use client';

import { useState, useCallback, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';

interface DadosEmpresa {
  empresa_id: number;
  nome_empresa: string;
  cnpj?: string;
  endereco_completo?: string;
  telefone?: string;
  email?: string;
  segmento?: string;
  descricao?: string;
  logo_url?: string;
  imagem_capa_url?: string;
  slug: string;
  status: 'ativo' | 'inativo' | 'pendente';
  horario_funcionamento?: any;
  stripe_account_id?: string;
  stripe_account_status?: string;
  pagamentos_online_habilitados: boolean;
  percentual_comissao_plataforma: number;
  created_at: string;
  updated_at: string;
}

interface PlanoSaas {
  plano_id: number;
  nome_plano: string;
  preco_mensal: number;
  limite_servicos: number;
  limite_colaboradores: number;
  recursos_premium: boolean;
  status_assinatura: 'ativa' | 'cancelada' | 'pausada' | 'expirada';
  data_inicio: string;
  data_fim?: string;
  stripe_subscription_id?: string;
}

interface MetricasEmpresa {
  total_agendamentos_mes: number;
  receita_bruta_mes: number;
  receita_liquida_mes: number;
  total_clientes_ativos: number;
  total_servicos_ativos: number;
  total_colaboradores_ativos: number;
  taxa_confirmacao_mes: number;
  crescimento_receita_percentual: number;
  agendamentos_pendentes: number;
  proximos_vencimentos: number;
}

interface StatusConfiguracao {
  empresa_configurada: boolean;
  stripe_configurado: boolean;
  servicos_cadastrados: boolean;
  horarios_definidos: boolean;
  colaboradores_ativos: boolean;
  percentual_conclusao: number;
  proximos_passos: string[];
}

interface EstadoEmpresaProprietario {
  empresa: DadosEmpresa | null;
  planoSaas: PlanoSaas | null;
  metricas: MetricasEmpresa | null;
  statusConfiguracao: StatusConfiguracao | null;
  loading: boolean;
  error: string | null;
  temEmpresa: boolean;
  empresaAtiva: boolean;
}

export function useEmpresaProprietario() {
  const { user } = useAuth();
  const [estado, setEstado] = useState<EstadoEmpresaProprietario>({
    empresa: null,
    planoSaas: null,
    metricas: null,
    statusConfiguracao: null,
    loading: false,
    error: null,
    temEmpresa: false,
    empresaAtiva: false
  });

  // Buscar dados completos da empresa
  const buscarDadosEmpresa = useCallback(async () => {
    if (!user) return;

    setEstado(prev => ({ ...prev, loading: true, error: null }));

    try {
      const response = await fetch('/api/proprietario/dashboard/empresa');
      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Erro ao buscar dados da empresa');
      }

      const { empresa, planoSaas, metricas, statusConfiguracao } = result.data;

      setEstado(prev => ({
        ...prev,
        empresa,
        planoSaas,
        metricas,
        statusConfiguracao,
        temEmpresa: !!empresa,
        empresaAtiva: empresa?.status === 'ativo',
        loading: false
      }));

    } catch (error: any) {
      setEstado(prev => ({
        ...prev,
        error: error.message,
        loading: false
      }));
    }
  }, [user]);

  // Atualizar dados da empresa
  const atualizarEmpresa = useCallback(async (dadosAtualizados: Partial<DadosEmpresa>) => {
    if (!estado.empresa) return false;

    try {
      const response = await fetch(`/api/empresas/${estado.empresa.empresa_id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(dadosAtualizados),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Erro ao atualizar empresa');
      }

      // Atualizar estado local
      setEstado(prev => ({
        ...prev,
        empresa: prev.empresa ? { ...prev.empresa, ...dadosAtualizados } : null
      }));

      return true;

    } catch (error: any) {
      setEstado(prev => ({
        ...prev,
        error: error.message
      }));
      return false;
    }
  }, [estado.empresa]);

  // Limpar erro
  const limparErro = useCallback(() => {
    setEstado(prev => ({ ...prev, error: null }));
  }, []);

  // Verificar se precisa de configuração inicial
  const precisaConfiguracaoInicial = useCallback(() => {
    if (!estado.statusConfiguracao) return true;
    return estado.statusConfiguracao.percentual_conclusao < 100;
  }, [estado.statusConfiguracao]);

  // Obter próximos passos
  const obterProximosPassos = useCallback(() => {
    return estado.statusConfiguracao?.proximos_passos || [];
  }, [estado.statusConfiguracao]);

  // Verificar se pode receber pagamentos
  const podeReceberPagamentos = useCallback(() => {
    return estado.empresa?.stripe_account_id && 
           estado.empresa?.pagamentos_online_habilitados &&
           estado.empresa?.stripe_account_status === 'active';
  }, [estado.empresa]);

  // Carregar dados iniciais
  useEffect(() => {
    if (user) {
      buscarDadosEmpresa();
    }
  }, [user, buscarDadosEmpresa]);

  return {
    // Estado
    empresa: estado.empresa,
    planoSaas: estado.planoSaas,
    metricas: estado.metricas,
    statusConfiguracao: estado.statusConfiguracao,
    loading: estado.loading,
    error: estado.error,
    temEmpresa: estado.temEmpresa,
    empresaAtiva: estado.empresaAtiva,

    // Ações
    buscarDadosEmpresa,
    atualizarEmpresa,
    limparErro,

    // Utilitários
    precisaConfiguracaoInicial,
    obterProximosPassos,
    podeReceberPagamentos
  };
}
