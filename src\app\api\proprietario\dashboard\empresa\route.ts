import { NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';

export async function GET() {
  try {
    const supabase = await createClient();

    // Verificar autenticação
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json(
        { success: false, error: 'Usuário não autenticado' },
        { status: 401 }
      );
    }

    // Buscar dados da empresa
    const { data: empresa, error: empresaError } = await supabase
      .from('empresas')
      .select(`
        empresa_id,
        nome_empresa,
        cnpj,
        endereco_completo,
        telefone,
        email,
        segmento,
        descricao,
        logo_url,
        imagem_capa_url,
        slug,
        status,
        horario_funcionamento,
        stripe_account_id,
        stripe_account_status,
        pagamentos_online_habilitados,
        percentual_comissao_plataforma,
        created_at,
        updated_at
      `)
      .eq('proprietario_user_id', user.id)
      .single();

    if (empresaError && empresaError.code !== 'PGRST116') {
      throw new Error('Erro ao buscar dados da empresa');
    }

    // Se não tem empresa, retornar dados vazios
    if (!empresa) {
      return NextResponse.json({
        success: true,
        data: {
          empresa: null,
          planoSaas: null,
          metricas: null,
          statusConfiguracao: {
            empresa_configurada: false,
            stripe_configurado: false,
            servicos_cadastrados: false,
            horarios_definidos: false,
            colaboradores_ativos: false,
            percentual_conclusao: 0,
            proximos_passos: ['Criar empresa']
          }
        }
      });
    }

    // Buscar plano SaaS
    const { data: assinaturaSaas } = await supabase
      .from('assinaturas_saas_empresas')
      .select(`
        planos_saas (
          plano_id,
          nome_plano,
          preco_mensal,
          limite_servicos,
          limite_colaboradores,
          recursos_premium
        ),
        status_assinatura,
        data_inicio,
        data_fim,
        stripe_subscription_id
      `)
      .eq('empresa_id', empresa.empresa_id)
      .eq('status_assinatura', 'ativa')
      .single();

    // Buscar métricas do mês atual
    const inicioMes = new Date();
    inicioMes.setDate(1);
    inicioMes.setHours(0, 0, 0, 0);

    const fimMes = new Date();
    fimMes.setMonth(fimMes.getMonth() + 1);
    fimMes.setDate(0);
    fimMes.setHours(23, 59, 59, 999);

    // Agendamentos do mês
    const { data: agendamentosMes } = await supabase
      .from('agendamentos')
      .select('status_agendamento, valor_total, created_at')
      .eq('empresa_id', empresa.empresa_id)
      .gte('created_at', inicioMes.toISOString())
      .lte('created_at', fimMes.toISOString());

    // Agendamentos pendentes
    const { data: agendamentosPendentes } = await supabase
      .from('agendamentos')
      .select('agendamento_id')
      .eq('empresa_id', empresa.empresa_id)
      .eq('status_agendamento', 'Pendente');

    // Clientes únicos do mês
    const { data: clientesUnicos } = await supabase
      .from('agendamentos')
      .select('cliente_user_id')
      .eq('empresa_id', empresa.empresa_id)
      .gte('created_at', inicioMes.toISOString())
      .lte('created_at', fimMes.toISOString());

    // Serviços ativos
    const { data: servicosAtivos } = await supabase
      .from('servicos')
      .select('servico_id')
      .eq('empresa_id', empresa.empresa_id)
      .eq('ativo', true);

    // Colaboradores ativos
    const { data: colaboradoresAtivos } = await supabase
      .from('colaboradores_empresa')
      .select('colaborador_empresa_id')
      .eq('empresa_id', empresa.empresa_id)
      .eq('ativo', true)
      .eq('convite_aceito', true);

    // Calcular métricas
    const totalAgendamentosMes = agendamentosMes?.length || 0;
    const agendamentosConfirmados = agendamentosMes?.filter(a => a.status_agendamento === 'Confirmado').length || 0;
    const receitaBrutaMes = agendamentosMes?.reduce((sum, a) => sum + (a.valor_total || 0), 0) || 0;
    const taxaConfirmacao = totalAgendamentosMes > 0 ? (agendamentosConfirmados / totalAgendamentosMes) * 100 : 0;
    
    const clientesAtivos = new Set(clientesUnicos?.map(c => c.cliente_user_id)).size;

    // Status de configuração
    const temServicos = (servicosAtivos?.length || 0) > 0;
    const temColaboradores = (colaboradoresAtivos?.length || 0) > 0;
    const temHorarios = !!empresa.horario_funcionamento;
    const temStripe = !!empresa.stripe_account_id;

    const statusConfiguracao = {
      empresa_configurada: true,
      stripe_configurado: temStripe,
      servicos_cadastrados: temServicos,
      horarios_definidos: temHorarios,
      colaboradores_ativos: temColaboradores,
      percentual_conclusao: [true, temStripe, temServicos, temHorarios, temColaboradores].filter(Boolean).length * 20,
      proximos_passos: [
        ...(!temStripe ? ['Configurar Stripe Connect'] : []),
        ...(!temServicos ? ['Cadastrar serviços'] : []),
        ...(!temHorarios ? ['Definir horários de funcionamento'] : []),
        ...(!temColaboradores ? ['Adicionar colaboradores'] : [])
      ]
    };

    const metricas = {
      total_agendamentos_mes: totalAgendamentosMes,
      receita_bruta_mes: receitaBrutaMes,
      receita_liquida_mes: receitaBrutaMes * (1 - empresa.percentual_comissao_plataforma / 100),
      total_clientes_ativos: clientesAtivos,
      total_servicos_ativos: servicosAtivos?.length || 0,
      total_colaboradores_ativos: colaboradoresAtivos?.length || 0,
      taxa_confirmacao_mes: taxaConfirmacao,
      crescimento_receita_percentual: 0, // TODO: Calcular comparando com mês anterior
      agendamentos_pendentes: agendamentosPendentes?.length || 0,
      proximos_vencimentos: 0 // TODO: Implementar lógica de vencimentos
    };

    return NextResponse.json({
      success: true,
      data: {
        empresa,
        planoSaas: assinaturaSaas ? {
          ...assinaturaSaas.planos_saas,
          status_assinatura: assinaturaSaas.status_assinatura,
          data_inicio: assinaturaSaas.data_inicio,
          data_fim: assinaturaSaas.data_fim,
          stripe_subscription_id: assinaturaSaas.stripe_subscription_id
        } : null,
        metricas,
        statusConfiguracao
      }
    });

  } catch (error: any) {
    console.error('Erro na API de dados da empresa:', error);
    return NextResponse.json(
      { success: false, error: error.message || 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}
